import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, map } from 'rxjs';
import { CheckboxOption } from '../models/checkbox.model';

@Injectable({
  providedIn: 'root'
})
export class CheckboxService {
  private http = inject(HttpClient);

  /**
   * Get predefined checkbox options with different variants
   */
  getCheckboxOptions(): Observable<CheckboxOption[]> {
    const mockCheckboxOptions: CheckboxOption[] = [
      {
        id: '1',
        label: 'Default Checkbox',
        value: 'default',
        variant: 'default',
        isChecked: false,
        indeterminate: false
      },
      {
        id: '2',
        label: 'With Background Checkbox',
        value: 'with-bg',
        variant: 'with-bg',
        isChecked: true,
        indeterminate: false
      },
      {
        id: '3',
        label: 'Animated Checkbox',
        value: 'animated',
        variant: 'animated',
        isChecked: false,
        indeterminate: false
      },
      {
        id: '4',
        label: 'Default Indeterminate',
        value: 'indeterminate-default',
        variant: 'default',
        isChecked: false,
        indeterminate: true
      },
      {
        id: '5',
        label: 'With-bg Indeterminate',
        value: 'indeterminate-with-bg',
        variant: 'with-bg',
        isChecked: false,
        indeterminate: true
      },
      {
        id: '6',
        label: 'Animated Indeterminate',
        value: 'indeterminate-animated',
        variant: 'animated',
        isChecked: false,
        indeterminate: true
      }
    ];

    return of(mockCheckboxOptions);
  }

  /**
   * Get checkbox options from external API (JSONPlaceholder users)
   * Transforms user data into checkbox options
   */
  getCheckboxOptionsFromAPI(): Observable<CheckboxOption[]> {
    return this.http.get<any[]>('https://jsonplaceholder.typicode.com/users').pipe(
      map(users => users.map((user, index) => ({
        id: user.id.toString(),
        label: user.name,
        value: user.username.toLowerCase(),
        variant: this.getVariantByIndex(index),
        isChecked: index % 3 === 0, // Every 3rd user is checked by default
        indeterminate: index % 5 === 0 && index % 3 !== 0 // Every 5th user (not already checked) is indeterminate
      })))
    );
  }

  /**
   * Get checkbox options based on categories (similar to dropdown cascading)
   */
  getCheckboxOptionsByCategory(categoryId: string): Observable<CheckboxOption[]> {
    return this.http.get<any[]>('https://jsonplaceholder.typicode.com/posts').pipe(
      map(posts => {
        const filteredPosts = posts.filter(post => post.userId.toString() === categoryId);
        return filteredPosts.slice(0, 5).map((post, index) => ({
          id: post.id.toString(),
          label: post.title.substring(0, 50) + (post.title.length > 50 ? '...' : ''),
          value: `post-${post.id}`,
          variant: this.getVariantByIndex(index),
          isChecked: false,
          indeterminate: false
        }));
      })
    );
  }

  /**
   * Save checkbox state to API (mock implementation)
   */
  saveCheckboxState(checkboxId: string, isChecked: boolean): Observable<any> {
    // In a real application, this would make a PUT/PATCH request to save the state
    console.log(`Saving checkbox ${checkboxId} state: ${isChecked}`);
    return of({ success: true, checkboxId, isChecked });
  }

  /**
   * Get variant based on index for variety
   */
  private getVariantByIndex(index: number): 'default' | 'with-bg' | 'animated' {
    const variants: ('default' | 'with-bg' | 'animated')[] = ['default', 'with-bg', 'animated'];
    return variants[index % variants.length];
  }
}
