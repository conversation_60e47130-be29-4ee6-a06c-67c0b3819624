import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { CheckboxOption } from '../models/checkbox.model';

@Injectable({
  providedIn: 'root'
})
export class HttpService {

  private http = inject(HttpClient);

  getPosts() {
    return this.http.get('https://jsonplaceholder.typicode.com/posts');
  }

  // Checkbox API methods
  getCheckboxOptions(): Observable<CheckboxOption[]> {
    // Simulating API call with mock data
    const mockCheckboxOptions: CheckboxOption[] = [
      {
        id: '1',
        label: 'Default Option',
        value: 'default',
        variant: 'default',
        isChecked: false,
        indeterminate: false
      },
      {
        id: '2',
        label: 'With Background Option',
        value: 'with-bg',
        variant: 'with-bg',
        isChecked: true,
        indeterminate: false
      },
      {
        id: '3',
        label: 'Animated Option',
        value: 'animated',
        variant: 'animated',
        isChecked: false,
        indeterminate: false
      },
      {
        id: '4',
        label: 'Indeterminate Default',
        value: 'indeterminate-default',
        variant: 'default',
        isChecked: false,
        indeterminate: true
      },
      {
        id: '5',
        label: 'Indeterminate With-bg',
        value: 'indeterminate-with-bg',
        variant: 'with-bg',
        isChecked: false,
        indeterminate: true
      },
      {
        id: '6',
        label: 'Indeterminate Animated',
        value: 'indeterminate-animated',
        variant: 'animated',
        isChecked: false,
        indeterminate: true
      }
    ];

    return of(mockCheckboxOptions);
  }

  // You can also create a method that fetches from a real API
  getCheckboxOptionsFromAPI(): Observable<any> {
    return this.http.get('https://jsonplaceholder.typicode.com/users');
  }
}
