# Checkbox API Implementation

This implementation provides a complete API-driven checkbox system similar to the dropdown component. It demonstrates how to fetch checkbox options from various data sources and manage their states.

## Features

- **Multiple Data Sources**: Load checkbox options from mock data or external APIs
- **State Management**: Track and persist checkbox states
- **Variant Support**: Support for different checkbox variants (default, with-bg, animated)
- **Indeterminate State**: Support for indeterminate checkboxes
- **Real-time Updates**: Dynamic state updates with visual feedback

## Files Structure

```
src/app/
├── models/
│   └── checkbox.model.ts          # TypeScript interfaces for checkbox data
├── service/
│   ├── checkbox.service.ts        # Dedicated checkbox API service
│   └── http.service.ts           # Extended with checkbox methods
└── app.component.*               # Main component with checkbox implementation
```

## Usage Examples

### 1. Basic Checkbox API Setup

```typescript
// Import the checkbox service and models
import { CheckboxService } from './service/checkbox.service';
import { CheckboxOption, CheckboxGroupState } from './models/checkbox.model';

// In your component
export class YourComponent {
  checkboxOptions = signal<CheckboxOption[]>([]);
  checkboxStates: CheckboxGroupState = {};

  constructor(private checkboxService: CheckboxService) {}

  loadCheckboxes() {
    this.checkboxService.getCheckboxOptions().subscribe(options => {
      this.checkboxOptions.set(options);
      // Initialize states
      options.forEach(option => {
        this.checkboxStates[option.id] = option.isChecked;
      });
    });
  }
}
```

### 2. Template Usage

```html
<!-- Load buttons -->
<ava-button label="Load Mock Data" (click)="loadCheckboxOptions()"></ava-button>
<ava-button label="Load from API" (click)="loadCheckboxOptionsFromAPI()"></ava-button>

<!-- Checkbox list -->
<div *ngFor="let option of checkboxOptions()">
  <ava-checkbox 
    [variant]="option.variant" 
    [label]="option.label" 
    [isChecked]="checkboxStates[option.id]" 
    [indeterminate]="option.indeterminate"
    (isCheckedChange)="onCheckboxChange(option, $event)">
  </ava-checkbox>
</div>
```

### 3. Available API Methods

#### CheckboxService Methods:

- `getCheckboxOptions()`: Returns predefined checkbox options with different variants
- `getCheckboxOptionsFromAPI()`: Fetches user data from JSONPlaceholder and converts to checkboxes
- `getCheckboxOptionsByCategory(categoryId)`: Gets checkboxes filtered by category
- `saveCheckboxState(checkboxId, isChecked)`: Saves checkbox state (mock implementation)

### 4. Data Models

```typescript
interface CheckboxOption {
  id: string;
  label: string;
  value: string;
  variant: 'default' | 'with-bg' | 'animated';
  isChecked: boolean;
  indeterminate: boolean;
}

interface CheckboxGroupState {
  [key: string]: boolean;
}
```

## API Integration Examples

### Mock Data Source
```typescript
loadCheckboxOptions(): void {
  this.checkboxService.getCheckboxOptions().subscribe(options => {
    // Handle predefined checkbox options
  });
}
```

### External API Source
```typescript
loadCheckboxOptionsFromAPI(): void {
  this.checkboxService.getCheckboxOptionsFromAPI().subscribe(options => {
    // Handle API-generated checkbox options
  });
}
```

### Category-based Loading
```typescript
loadCheckboxesByCategory(categoryId: string): void {
  this.checkboxService.getCheckboxOptionsByCategory(categoryId).subscribe(options => {
    // Handle category-filtered checkboxes
  });
}
```

## State Management

The implementation includes comprehensive state management:

1. **Local State**: `checkboxStates` object tracks current checkbox states
2. **Signal Updates**: `checkboxOptions` signal provides reactive updates
3. **Persistence**: Optional API calls to save state changes
4. **Visual Feedback**: Real-time UI updates with color-coded states

## Integration with Existing Components

This checkbox API follows the same pattern as the dropdown implementation:

- Similar service structure
- Consistent data fetching patterns
- Compatible state management
- Reusable across components

## Customization

You can extend the implementation by:

1. Adding new checkbox variants
2. Implementing different data sources
3. Adding validation logic
4. Integrating with form controls
5. Adding bulk operations (select all, clear all)

## Testing

The implementation includes console logging for debugging:
- Checkbox state changes
- API responses
- State persistence calls

Check the browser console to monitor checkbox interactions and API calls.
