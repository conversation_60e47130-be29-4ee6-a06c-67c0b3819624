<div style="padding:50px;border:1px solid #ccc;width:500px;margin: 0 auto;margin-top: 100px;">

    <div style="width:400px;margin-top: 30px;">
        <ava-button label="Primary" variant="primary" width="160px"></ava-button>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-calendar (dateSelected)="onDateSelected($event)"></ava-calendar>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-link label="Underlined Link" color="success" [underline]="true"></ava-link>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-slider [value]=".3" [min]="0" [max]="100"></ava-slider>
    </div>


    <div style="width:400px;margin-top: 30px;">
        <ava-textarea label="Basic Textarea" placeholder="Type here..."></ava-textarea>
    </div>

    <div style="width:400px;margin-top: 30px;">
        <ava-textbox label="Email" type="email" placeholder="<EMAIL>">
        </ava-textbox>
    </div>
    <div style="width:400px;margin-top: 30px;">
        <ava-text-card [title]="'Create Tool'" [iconName]="'plus'" [type]="'create'" iconColor="#144692">
        </ava-text-card>
    </div>

    <!-- Checkbox API Demo Section -->
    <div class="checkbox-api-demo" style="width:500px;">
        <h3>Checkbox API Demo</h3>
        <p style="color: #666; margin-bottom: 20px;">
            This demonstrates a checkbox API similar to the dropdown implementation.
            Load data from different sources and see real-time state management.
        </p>

        <!-- API Source Selection Buttons -->
        <div class="api-buttons">
            <ava-button
                label="Load Mock Data"
                variant="primary"
                width="140px"
                (click)="loadCheckboxOptions()">
            </ava-button>
            <ava-button
                label="Load from API"
                variant="secondary"
                width="140px"
                (click)="loadCheckboxOptionsFromAPI()">
            </ava-button>
            <ava-button
                label="Clear"
                variant="secondary"
                width="80px"
                (click)="clearCheckboxOptions()">
            </ava-button>
        </div>

        <div *ngIf="checkboxLoading" class="loading-message">
            🔄 Loading checkboxes...
        </div>

        <div *ngIf="!checkboxLoading && checkboxOptions().length === 0" class="empty-message">
            👆 Click a button above to load checkbox options
        </div>

        <div class="checkbox-list" *ngIf="checkboxOptions().length > 0">
            <div *ngFor="let option of checkboxOptions()" class="checkbox-item">
                <ava-checkbox
                    [variant]="option.variant"
                    [label]="option.label"
                    [isChecked]="checkboxStates[option.id]"
                    [indeterminate]="option.indeterminate"
                    (isCheckedChange)="onCheckboxChange(option, $event)">
                </ava-checkbox>
            </div>
        </div>

        <!-- Display current states -->
        <div *ngIf="checkboxOptions().length > 0" class="states-display">
            <h4>📊 Current Checkbox States</h4>
            <div *ngFor="let option of checkboxOptions()" class="state-item">
                <span class="state-label">{{ option.label }}</span>
                <span class="state-value"
                      [ngClass]="checkboxStates[option.id] ? 'state-checked' : 'state-unchecked'">
                    {{ checkboxStates[option.id] ? '✓ Checked' : '✗ Unchecked' }}
                </span>
            </div>
        </div>
    </div>

</div>



<div class="cascading-dropdown-demo">

    <div class="demo-header">

        <h2>Cascading Dropdown Example</h2>

        <p>Select a category to enable the second dropdown with filtered options.</p>

    </div>



    <div class="dropdown-container">
        <!-- First Dropdown - Category Selection -->
        <div class="dropdown-section">
            <h4>Step 1: Select Category</h4>

            <ava-dropdown [disabled]="categoryOptions1.length <1" dropdownTitle="Select Category"
                [options]="categoryOptions1" [search]="true" (selectionChange)="onCategoryChange($event)">

            </ava-dropdown>{{categoryOptions().length}}

        </div>



        <!-- Second Dropdown - Sub-category (Disabled until first selection) -->

        <!-- <div class="dropdown-section">

            <h4>Step 2: Select Item</h4>

            <ng-container *ngIf="!isSecondDropdownDisabled; else disabledDropdown">

                <ava-dropdown [dropdownTitle]="getSecondDropdownTitle()" [options]="subCategoryOptions"
                    [disabled]="false" [search]="true" [selectedValue]="''"
                    (selectionChange)="onSubCategoryChange($event)">

                </ava-dropdown>

            </ng-container>



            <ng-template #disabledDropdown>

                <ava-dropdown [dropdownTitle]="getSecondDropdownTitle()" [options]="[]" [disabled]="true"
                    [search]="true">

                </ava-dropdown>

            </ng-template>

        </div>

    </div> -->



    <!-- Display Current Selections -->

    <!-- <div class="selections-display" *ngIf="selectedCategory || selectedSubCategory">

        <h4>Current Selections:</h4>

        <div class="selection-item" *ngIf="selectedCategory">

            <strong>Category:</strong> {{ selectedCategory }} ({{ selectedCategoryValue }})

        </div>

        <div class="selection-item" *ngIf="selectedSubCategory">

            <strong>{{ selectedCategory }}:</strong> {{ selectedSubCategory }} ({{ selectedSubCategoryValue }})

        </div> -->

    </div>