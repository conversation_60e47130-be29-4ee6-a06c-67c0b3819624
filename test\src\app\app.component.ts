import { CommonModule } from '@angular/common';
import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { ButtonComponent, AvaTextareaComponent, CheckboxComponent } from '@ava/play-comp-library';
import { CalendarComponent } from '@ava/play-comp-library';
import { LinkComponent } from '@ava/play-comp-library';
import { SliderComponent } from '@ava/play-comp-library';
import { TextCardComponent } from '@ava/play-comp-library';
import { AvaTextboxComponent } from '@ava/play-comp-library';
import { DropdownComponent } from '@ava/play-comp-library';
import { HttpService } from './service/http.service';
import { CheckboxService } from './service/checkbox.service';
import { CheckboxOption, CheckboxChangeEvent, CheckboxGroupState } from './models/checkbox.model';

@Component({
  selector: 'app-root',
  imports: [CommonModule,
    ButtonComponent,
    CalendarComponent,
    LinkComponent,
    SliderComponent,
    TextCardComponent,
    AvaTextboxComponent,
    DropdownComponent,
    AvaTextareaComponent,
    CheckboxComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'play-check';
  sliderValue = signal(50);
  active = true;

  // Dropdown properties
  categoryOptions = signal<any[]>([]);
  categoryOptions1: any[] = [];
  categoryDisabled = false;

  // Checkbox properties
  checkboxOptions = signal<CheckboxOption[]>([]);
  checkboxStates: CheckboxGroupState = {};
  checkboxLoading = false;

  constructor(
    private httpService: HttpService,
    private checkboxService: CheckboxService
  ) {

  }
  onValueChange(event: any) {
    console.log(event);
  }

  dark() {
    document.documentElement.classList.remove('light-theme');
    document.documentElement.classList.add('dark-theme');
    this.active = false;
  }
  light() {
    document.documentElement.classList.remove('dark-theme');
    document.documentElement.classList.add('light-theme');
    this.active = true;
  }

  onDateSelected(event: any) {
    console.log('event calendar', event);
  }
  /// Dropdown
  ngOnInit(): void {
    this.categoryDisabled = false;
    this.httpService.getPosts().subscribe((data: any) => {
      console.log('data', data);
      const cat: any[] = [];
      data.forEach((element: any) => {
        let c = {
          name: element.userId,
          value: element.userId,
        }
        cat.push(c);
      });
      console.log(cat);
      this.categoryOptions.set(cat);
      this.categoryDisabled = true;
      this.categoryOptions1 = cat;
    });

    // Checkbox options will be loaded when user clicks the buttons
  }

  onCategoryChange(event: any) {

  }

  // Checkbox methods
  loadCheckboxOptions(): void {
    this.checkboxLoading = true;
    this.checkboxService.getCheckboxOptions().subscribe((options: CheckboxOption[]) => {
      console.log('Checkbox options loaded:', options);
      this.checkboxOptions.set(options);

      // Initialize checkbox states
      options.forEach(option => {
        this.checkboxStates[option.id] = option.isChecked;
      });

      this.checkboxLoading = false;
    });
  }

  // Load checkboxes from external API
  loadCheckboxOptionsFromAPI(): void {
    this.checkboxLoading = true;
    this.checkboxService.getCheckboxOptionsFromAPI().subscribe((options: CheckboxOption[]) => {
      console.log('Checkbox options from API loaded:', options);
      this.checkboxOptions.set(options);

      // Initialize checkbox states
      options.forEach(option => {
        this.checkboxStates[option.id] = option.isChecked;
      });

      this.checkboxLoading = false;
    });
  }

  onCheckboxChange(option: CheckboxOption, isChecked: boolean): void {
    console.log('Checkbox changed:', option.label, 'Checked:', isChecked);

    // Update the state
    this.checkboxStates[option.id] = isChecked;

    // Update the option in the signal
    const currentOptions = this.checkboxOptions();
    const updatedOptions = currentOptions.map(opt =>
      opt.id === option.id ? { ...opt, isChecked } : opt
    );
    this.checkboxOptions.set(updatedOptions);

    // Save state to API (optional)
    this.checkboxService.saveCheckboxState(option.id, isChecked).subscribe(
      response => console.log('Checkbox state saved:', response)
    );

    console.log('Current checkbox states:', this.checkboxStates);
  }

  // Clear all checkbox options and states
  clearCheckboxOptions(): void {
    this.checkboxOptions.set([]);
    this.checkboxStates = {};
    console.log('Checkbox options cleared');
  }

  ////


}
