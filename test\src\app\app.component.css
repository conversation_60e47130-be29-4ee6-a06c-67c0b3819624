.slider{
  margin-top: 2rem;
  width: 400px;
  margin:50px;
}

/* Checkbox API Demo Styles */
.checkbox-api-demo {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
  background-color: #fafafa;
}

.checkbox-api-demo h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.api-buttons {
  display: flex;
  gap: 10px;
  margin: 15px 0;
  flex-wrap: wrap;
}

.checkbox-list {
  margin: 20px 0;
  max-height: 300px;
  overflow-y: auto;
}

.checkbox-item {
  margin: 10px 0;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.checkbox-item:hover {
  background-color: #f0f0f0;
}

.states-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.states-display h4 {
  margin-top: 0;
  color: #495057;
}

.state-item {
  margin: 8px 0;
  padding: 4px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.state-label {
  font-weight: 500;
  color: #343a40;
}

.state-value {
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.85em;
}

.state-checked {
  color: #155724;
  background-color: #d4edda;
}

.state-unchecked {
  color: #721c24;
  background-color: #f8d7da;
}

.loading-message {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.empty-message {
  color: #6c757d;
  text-align: center;
  padding: 20px;
  font-style: italic;
}